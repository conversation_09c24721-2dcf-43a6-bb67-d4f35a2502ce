-- Optimize RLS policies for cases table
-- Performance improvement: auth.uid() → (SELECT auth.uid())
-- This addresses the auth_rls_initplan performance issue

-- Drop existing policies
DROP POLICY IF EXISTS "Company users can manage their company cases" ON public.cases;
DROP POLICY IF EXISTS "Super admins can manage all cases" ON public.cases;

-- <PERSON>reate optimized policies using (SELECT auth.uid()) pattern
CREATE POLICY "Company users can manage their company cases" ON public.cases
  FOR ALL USING (
    company_id = public.get_user_company_id((SELECT auth.uid()))
  );

CREATE POLICY "Super admins can manage all cases" ON public.cases
  FOR ALL USING (
    public.is_super_admin((SELECT auth.uid()))
  );

-- Performance testing query
-- This query should be faster after optimization
SELECT 
  'Cases RLS Performance Test - Post Optimization' as test_name,
  COUNT(*) as record_count,
  EXTRACT(EPOCH FROM (clock_timestamp() - statement_timestamp())) * 1000 as execution_time_ms
FROM cases 
WHERE created_at >= NOW() - INTERVAL '30 days';

-- Test the join performance as well
SELECT 
  'Cases Join Performance Test - Post Optimization' as test_name,
  COUNT(*) as record_count,
  EXTRACT(EPOCH FROM (clock_timestamp() - statement_timestamp())) * 1000 as execution_time_ms
FROM cases c
JOIN case_types ct ON c.case_type_id = ct.id
WHERE c.created_at >= NOW() - INTERVAL '30 days';

-- Add comments for documentation
COMMENT ON POLICY "Company users can manage their company cases" ON public.cases 
IS 'Optimized RLS policy using (SELECT auth.uid()) for better performance - addresses auth_rls_initplan issue';

COMMENT ON POLICY "Super admins can manage all cases" ON public.cases 
IS 'Optimized RLS policy using (SELECT auth.uid()) for better performance - addresses auth_rls_initplan issue';

-- Verify policies are active
SELECT 
  'Cases RLS Policies Verification' as test_name,
  COUNT(*) as policy_count
FROM pg_policies 
WHERE tablename = 'cases' AND schemaname = 'public';
