-- Fix pending_task_reminders view security issues
-- This migration addresses the Supabase security recommendation for SECURITY DEFINER view

-- ANALYSIS FINDINGS:
-- 1. The pending_task_reminders view is owned by postgres user (security risk)
-- 2. The view IS used by task-reminder-processor Edge Function
-- 3. The view exposes sensitive company API credentials (green_api_instance_id, green_api_token)
-- 4. Regular users could potentially access other companies' reminder data

-- SECURITY ISSUE:
-- The view runs with postgres privileges and can bypass RLS policies,
-- potentially exposing sensitive API credentials and reminder data across companies

-- SOLUTION:
-- Recreate the view with proper ownership and implement company-based access control
-- while preserving functionality for the Edge Function (which uses service_role)

-- First, drop the existing view
DROP VIEW IF EXISTS public.pending_task_reminders;

-- Recreate the view with the same functionality but proper security
CREATE VIEW public.pending_task_reminders AS
SELECT 
  tr.id as reminder_id,
  tr.task_id,
  tr.company_id,
  tr.reminder_type,
  tr.scheduled_for,
  tr.status,
  ct.title as task_title,
  ct.description as task_description,
  ct.deadline as task_deadline,
  ct.priority as task_priority,
  ct.assigned_to as task_assigned_to,
  c.id as case_id,
  c.title as case_title,
  c.assigned_user_id as case_assigned_user_id,
  comp.green_api_instance_id,
  comp.green_api_token
FROM public.task_reminders tr
JOIN public.case_tasks ct ON tr.task_id = ct.id
JOIN public.cases c ON ct.case_id = c.id
JOIN public.companies comp ON tr.company_id = comp.id
WHERE tr.status = 'pending' 
  AND tr.scheduled_for <= NOW()
  AND ct.status != 'הושלם'
  AND comp.green_api_instance_id IS NOT NULL 
  AND comp.green_api_token IS NOT NULL;

-- Enable RLS on the view (this will inherit from underlying tables)
-- Note: Views don't have RLS directly, but access is controlled by underlying table RLS

-- Grant appropriate permissions
-- Regular users can only see reminders from their company (via underlying table RLS)
GRANT SELECT ON public.pending_task_reminders TO authenticated;

-- Service role can see all reminders (needed for Edge Function processing)
GRANT SELECT ON public.pending_task_reminders TO service_role;

-- Create a company-filtered function for regular user access
-- This provides an additional security layer for sensitive API credentials
CREATE OR REPLACE FUNCTION public.get_company_pending_reminders(p_company_id UUID DEFAULT NULL)
RETURNS TABLE (
  reminder_id UUID,
  task_id UUID,
  company_id UUID,
  reminder_type TEXT,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  status TEXT,
  task_title TEXT,
  task_description TEXT,
  task_deadline TIMESTAMP WITH TIME ZONE,
  task_priority TEXT,
  task_assigned_to UUID,
  case_id UUID,
  case_title TEXT,
  case_assigned_user_id UUID
) 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_company_id UUID;
BEGIN
  -- Get the user's company ID if not provided
  IF p_company_id IS NULL THEN
    SELECT ur.company_id INTO user_company_id
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid()
    LIMIT 1;
  ELSE
    -- Verify user has access to the requested company
    IF NOT EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.user_id = auth.uid() 
      AND (ur.company_id = p_company_id OR ur.role = 'super_admin')
    ) THEN
      RAISE EXCEPTION 'Access denied to company data';
    END IF;
    user_company_id := p_company_id;
  END IF;

  -- Return reminders without sensitive API credentials
  RETURN QUERY
  SELECT 
    ptr.reminder_id,
    ptr.task_id,
    ptr.company_id,
    ptr.reminder_type,
    ptr.scheduled_for,
    ptr.status,
    ptr.task_title,
    ptr.task_description,
    ptr.task_deadline,
    ptr.task_priority,
    ptr.task_assigned_to,
    ptr.case_id,
    ptr.case_title,
    ptr.case_assigned_user_id
  FROM public.pending_task_reminders ptr
  WHERE ptr.company_id = user_company_id;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.get_company_pending_reminders(UUID) TO authenticated;

-- Add comments for documentation
COMMENT ON VIEW public.pending_task_reminders IS 'View for pending task reminders - used by Edge Functions with service_role access';
COMMENT ON FUNCTION public.get_company_pending_reminders(UUID) IS 'Secure function for users to access their company reminders without API credentials';

-- Note: The Edge Function (task-reminder-processor) will continue to work because:
-- 1. It uses service_role key which bypasses RLS
-- 2. It can access the full view including API credentials
-- 3. Regular users are protected by underlying table RLS and the secure function
