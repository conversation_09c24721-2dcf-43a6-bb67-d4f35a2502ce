-- Add manual trigger type to marketing automation workflows
-- This migration safely adds 'manual' to the existing trigger_type constraint

-- First, drop the existing constraint
ALTER TABLE public.workflows 
DROP CONSTRAINT IF EXISTS workflows_trigger_type_check;

-- Add the new constraint with 'manual' included
ALTER TABLE public.workflows 
ADD CONSTRAINT workflows_trigger_type_check 
CHECK (trigger_type IN ('lead_status_change', 'case_status_change', 'manual'));

-- Update the comment to reflect the new trigger type
COMMENT ON COLUMN public.workflows.trigger_type IS 'Type of trigger: lead_status_change, case_status_change, or manual';

-- For manual triggers, the trigger_config will store:
-- {
--   "description": "Manual campaign description"      -- Optional description
-- }
-- Note: target_statuses are selected at execution time for maximum flexibility
COMMENT ON COLUMN public.workflows.trigger_config IS 'JSON config: For status changes: {"from_status": "any|specific", "to_status": "specific_status"}. For manual: {"description": "optional"}';
