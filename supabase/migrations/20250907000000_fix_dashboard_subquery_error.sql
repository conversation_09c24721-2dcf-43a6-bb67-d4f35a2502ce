-- Fix dashboard metrics function subquery error
-- The issue is in the revenue_by_month section where there are nested GROUP BY clauses

CREATE OR REPLACE FUNCTION public.get_dashboard_metrics(
  p_company_id UUID,
  p_date_from TIMESTAMP WITH TIME ZONE,
  p_date_to TIMESTAMP WITH TIME ZONE
)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Check if user has access to this company
  IF NOT (
    public.is_super_admin((SELECT auth.uid())) OR
    p_company_id IN (
      SELECT company_id FROM public.user_roles
      WHERE user_id = (SELECT auth.uid())
    )
  ) THEN
    RAISE EXCEPTION 'Access denied to company data';
  END IF;

  -- Build comprehensive dashboard metrics with nested structure
  SELECT json_build_object(
    'leads', (
      SELECT json_build_object(
        'total', (
          SELECT COUNT(*)
          FROM leads l
          WHERE l.company_id = p_company_id
          AND l.created_at BETWEEN p_date_from AND p_date_to
        ),
        'closed', (
          SELECT COUNT(*)
          FROM leads l
          WHERE l.company_id = p_company_id
          AND l.created_at BETWEEN p_date_from AND p_date_to
          AND l.status = 'לקוח סגור'
        ),
        'close_rate', (
          SELECT CASE
            WHEN COUNT(*) > 0 THEN (COUNT(*) FILTER (WHERE status = 'לקוח סגור') * 100.0 / COUNT(*))
            ELSE 0
          END
          FROM leads l
          WHERE l.company_id = p_company_id
          AND l.created_at BETWEEN p_date_from AND p_date_to
        ),
        'total_value', (
          SELECT COALESCE(SUM(value), 0)
          FROM cases
          WHERE company_id = p_company_id
          AND created_at BETWEEN p_date_from AND p_date_to
        ),
        'closed_deals_revenue', (
          SELECT COALESCE(SUM(value), 0)
          FROM cases
          WHERE company_id = p_company_id
          AND created_at BETWEEN p_date_from AND p_date_to
          AND status = 'סגור'
        ),
        'by_status', (
          SELECT COALESCE(json_agg(
            json_build_object(
              'status', status,
              'count', status_count,
              'value', status_value
            )
          ), '[]'::json)
          FROM (
            SELECT
              status,
              COUNT(*) as status_count,
              COALESCE(SUM(value), 0) as status_value
            FROM leads l
            WHERE l.company_id = p_company_id
            AND l.created_at BETWEEN p_date_from AND p_date_to
            GROUP BY status
          ) lead_status_data
        )
      )
    ),
    'cases', (
      SELECT json_build_object(
        'total', COUNT(*),
        'active', COUNT(*) FILTER (WHERE status != 'סגור'),
        'completed', COUNT(*) FILTER (WHERE status = 'סגור'),
        'by_type', COALESCE(
          json_agg(
            json_build_object(
              'type', COALESCE(case_type_name, 'ללא סוג'),
              'count', type_count,
              'value', type_value
            )
          ) FILTER (WHERE case_type_name IS NOT NULL OR type_count > 0),
          '[]'::json
        ),
        'by_status', COALESCE(
          json_agg(
            json_build_object(
              'status', status,
              'count', status_count
            )
          ) FILTER (WHERE status IS NOT NULL),
          '[]'::json
        )
      )
      FROM (
        SELECT
          ct.name as case_type_name,
          COUNT(*) as type_count,
          COALESCE(SUM(c.value), 0) as type_value,
          c.status,
          COUNT(*) OVER (PARTITION BY c.status) as status_count
        FROM cases c
        LEFT JOIN case_types ct ON c.case_type_id = ct.id
        WHERE c.company_id = p_company_id
        AND c.created_at BETWEEN p_date_from AND p_date_to
        GROUP BY ct.name, c.status
      ) case_data
    ),
    'time_entries', (
      SELECT json_build_object(
        'total_hours', COALESCE(SUM(duration), 0) / 60.0,
        'billable_hours', COALESCE(SUM(duration) FILTER (WHERE hourly_rate > 0), 0) / 60.0,
        'total_cost', COALESCE(SUM(total_cost), 0),
        'average_hourly_rate', (
          SELECT CASE
            WHEN SUM(te.duration) > 0 THEN
              (SELECT COALESCE(SUM(c.value), 0) FROM cases c WHERE c.company_id = p_company_id AND c.created_at BETWEEN p_date_from AND p_date_to) / (SUM(te.duration) / 60.0)
            ELSE 0
          END
          FROM case_time_entries te
          WHERE te.company_id = p_company_id
          AND te.created_at BETWEEN p_date_from AND p_date_to
        ),
        'utilization_rate', 85.0,
        'average_case_duration', 30.0
      )
      FROM case_time_entries te
      WHERE te.company_id = p_company_id
      AND te.created_at BETWEEN p_date_from AND p_date_to
    ),
    'revenue_by_month', (
      SELECT COALESCE(json_agg(
        json_build_object(
          'month', month_year,
          'revenue', monthly_revenue,
          'hours', monthly_hours,
          'closed_deals', monthly_deals
        ) ORDER BY month_year
      ), '[]'::json)
      FROM (
        SELECT
          month_year,
          SUM(monthly_revenue) as monthly_revenue,
          SUM(monthly_hours) as monthly_hours,
          SUM(monthly_deals) as monthly_deals
        FROM (
          SELECT
            to_char(created_at, 'YYYY-MM') as month_year,
            COALESCE(SUM(value) FILTER (WHERE status = 'סגור'), 0) as monthly_revenue,
            0::numeric as monthly_hours,
            COUNT(*) FILTER (WHERE status = 'סגור') as monthly_deals
          FROM cases
          WHERE company_id = p_company_id
          AND created_at BETWEEN p_date_from AND p_date_to
          GROUP BY to_char(created_at, 'YYYY-MM')

          UNION ALL

          SELECT
            to_char(created_at, 'YYYY-MM') as month_year,
            0::numeric as monthly_revenue,
            COALESCE(SUM(duration), 0) / 60.0 as monthly_hours,
            0::bigint as monthly_deals
          FROM case_time_entries
          WHERE company_id = p_company_id
          AND created_at BETWEEN p_date_from AND p_date_to
          GROUP BY to_char(created_at, 'YYYY-MM')
        ) monthly_data
        GROUP BY month_year
      ) aggregated_monthly_data
    )
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_dashboard_metrics(UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) TO authenticated;
