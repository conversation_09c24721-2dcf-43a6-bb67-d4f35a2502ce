-- Fix workflow_triggers R<PERSON> security issues
-- This migration addresses the Supabase security recommendation for <PERSON><PERSON> disabled in public
--
-- ISSUE: Currently users can see workflow triggers from ALL companies
-- SOLUTION: Enable RLS with company-based isolation while preserving workflow functionality

-- First, ensure R<PERSON> is enabled
ALTER TABLE public.workflow_triggers ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate them properly
DROP POLICY IF EXISTS "System can manage triggers" ON public.workflow_triggers;
DROP POLICY IF EXISTS "Users can view triggers from their company" ON public.workflow_triggers;

-- Create company-based RLS policies that allow users to work with triggers from their company only

-- Policy for ALL operations - users can manage triggers from their company
-- This covers SELECT, INSERT, UPDATE, DELETE for company members
CREATE POLICY "Company members can manage their workflow triggers" ON public.workflow_triggers
  FOR ALL USING (
    company_id IN (
      SELECT company_id FROM public.user_roles
      WHERE user_id = auth.uid()
    )
  );

-- Special policy for super admins to access all triggers (for admin/debugging purposes)
CREATE POLICY "Super admins can access all workflow triggers" ON public.workflow_triggers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'super_admin'
    )
  );

-- Ensure the workflow trigger functions have proper security
-- Update the case workflow trigger function to use SECURITY DEFINER properly
CREATE OR REPLACE FUNCTION public.create_case_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create trigger if status actually changed
  IF TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id
    ) VALUES (
      'case',
      NEW.id,
      OLD.status,
      NEW.status,
      NEW.company_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Update the lead workflow trigger function to use SECURITY DEFINER properly
CREATE OR REPLACE FUNCTION public.create_lead_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create trigger if status actually changed
  IF TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id
    ) VALUES (
      'lead',
      NEW.id,
      OLD.status,
      NEW.status,
      NEW.company_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Add comments for documentation
COMMENT ON TABLE public.workflow_triggers IS 'Stores trigger events for workflow automation system with company-based RLS';
COMMENT ON POLICY "Company members can manage their workflow triggers" ON public.workflow_triggers IS 'Users can manage workflow triggers from their company only - ensures data isolation';
COMMENT ON POLICY "Super admins can access all workflow triggers" ON public.workflow_triggers IS 'Super admins can access workflow triggers across all companies for administrative purposes';

-- Note: Edge Functions use service_role key so they can process all triggers regardless of RLS
-- This maintains the workflow automation functionality while securing user access
