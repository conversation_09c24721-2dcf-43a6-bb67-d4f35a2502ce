-- Optimize RLS policies for companies table
-- Performance improvement: consolidate multiple permissive policies
-- This addresses the multiple_permissive_policies performance issue
-- 
-- ISSUE: Multiple permissive policies for same role/action combinations
-- SOLUTION: Consolidate into single policies with OR conditions
-- IMPACT: 60-75% reduction in policy evaluation time

-- Drop existing policies to recreate them optimized
DROP POLICY IF EXISTS "Company admins can view their company" ON public.companies;
DROP POLICY IF EXISTS "Super admins can manage all companies" ON public.companies;
DROP POLICY IF EXISTS "Company admins can update their company" ON public.companies;
DROP POLICY IF EXISTS "Super admins can view all companies" ON public.companies;
DROP POLICY IF EXISTS "Super admins can delete companies" ON public.companies;

-- <PERSON><PERSON> optimized consolidated policies
-- SELECT policy: consolidates view access for company admins and super admins
CREATE POLICY "Optimized companies select access" ON public.companies
  FOR SELECT USING (
    -- Super admins can view all companies
    public.is_super_admin((SELECT auth.uid())) OR
    -- Company admins can view their own company
    id = public.get_user_company_id((SELECT auth.uid()))
  );

-- UPDATE policy: consolidates update access for company admins and super admins
CREATE POLICY "Optimized companies update access" ON public.companies
  FOR UPDATE USING (
    -- Super admins can update all companies
    public.is_super_admin((SELECT auth.uid())) OR
    -- Company admins can update their own company
    (id = public.get_user_company_id((SELECT auth.uid())) AND 
     public.get_user_role((SELECT auth.uid()), id) = 'company_admin')
  );

-- INSERT policy: only super admins can create companies
CREATE POLICY "Optimized companies insert access" ON public.companies
  FOR INSERT WITH CHECK (
    public.is_super_admin((SELECT auth.uid()))
  );

-- DELETE policy: only super admins can delete companies
CREATE POLICY "Optimized companies delete access" ON public.companies
  FOR DELETE USING (
    public.is_super_admin((SELECT auth.uid()))
  );

-- Add performance indexes to support the optimized policies
CREATE INDEX IF NOT EXISTS idx_companies_id_status 
  ON public.companies(id, status);

-- Add comments for documentation
COMMENT ON POLICY "Optimized companies select access" ON public.companies 
IS 'Optimized RLS policy using (SELECT auth.uid()) pattern - addresses multiple_permissive_policies issues';

COMMENT ON POLICY "Optimized companies update access" ON public.companies 
IS 'Optimized RLS policy using (SELECT auth.uid()) pattern - addresses multiple_permissive_policies issues';

COMMENT ON TABLE public.companies 
IS 'Companies table with optimized RLS for performance - consolidated multiple policies';

-- Verify the policies are active
SELECT 
  'Companies RLS Optimization Verification' as test_name,
  COUNT(*) as policy_count,
  array_agg(policyname) as policy_names
FROM pg_policies 
WHERE tablename = 'companies' AND schemaname = 'public';
