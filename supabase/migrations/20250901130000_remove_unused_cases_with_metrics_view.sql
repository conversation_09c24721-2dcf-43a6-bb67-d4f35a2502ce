-- Remove unused cases_with_metrics view that poses security risk
-- This migration addresses the Supabase security recommendation for SECURITY DEFINER view

-- ANALYSIS FINDINGS:
-- 1. The cases_with_metrics view is owned by postgres user (security risk)
-- 2. The view is NOT used anywhere in the application code
-- 3. All case queries use the cases table directly with proper RLS
-- 4. The view calculates metrics that can be computed in application code when needed

-- SECURITY ISSUE:
-- The view runs with postgres privileges and can bypass RLS policies,
-- potentially exposing case data across companies

-- SOLUTION:
-- Remove the unused view entirely since it's not needed by the application

-- First, check if any dependencies exist (should be none based on code analysis)
-- This will fail if there are dependencies, preventing accidental removal
DO $$
BEGIN
  -- Check for any views or functions that depend on cases_with_metrics
  IF EXISTS (
    SELECT 1 FROM pg_depend d
    JOIN pg_class c ON d.objid = c.oid
    WHERE d.refobjid = (
      SELECT oid FROM pg_class 
      WHERE relname = 'cases_with_metrics' AND relnamespace = (
        SELECT oid FROM pg_namespace WHERE nspname = 'public'
      )
    )
    AND c.relkind IN ('v', 'f') -- views or functions
    AND c.relname != 'cases_with_metrics' -- exclude self-reference
  ) THEN
    RAISE EXCEPTION 'Cannot remove cases_with_metrics view: dependencies exist';
  END IF;
END $$;

-- Remove the view
DROP VIEW IF EXISTS public.cases_with_metrics;

-- Add comment for audit trail
COMMENT ON SCHEMA public IS 'Removed cases_with_metrics view on 2025-09-01 - unused view with security risk (SECURITY DEFINER owned by postgres)';

-- Note: If case metrics are needed in the future, they should be:
-- 1. Calculated in application code using proper RLS-compliant queries
-- 2. Implemented as a SECURITY DEFINER function with explicit permission checks
-- 3. Created as a materialized view with proper ownership and refresh policies

-- The application already handles case metrics correctly by:
-- - Querying cases table directly with company_id filtering
-- - Using proper RLS policies on the cases table
-- - Calculating metrics in TypeScript code when needed
