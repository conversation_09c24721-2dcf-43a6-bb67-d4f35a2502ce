-- Fix WhatsApp RLS policies and conversation deletion issues
-- This migration addresses missing DELETE policies and incorrect message policies

-- Drop existing incorrect policies
DROP POLICY IF EXISTS "Company members can manage their company WhatsApp conversations" ON public.whatsapp_conversations;
DROP POLICY IF EXISTS "Company members can manage their company WhatsApp messages" ON public.whatsapp_messages;
DROP POLICY IF EXISTS "Users can view company conversations" ON public.whatsapp_conversations;
DROP POLICY IF EXISTS "Users can create company conversations" ON public.whatsapp_conversations;
DROP POLICY IF EXISTS "Users can update company conversations" ON public.whatsapp_conversations;
DROP POLICY IF EXISTS "Users can view company messages" ON public.whatsapp_messages;
DROP POLICY IF EXISTS "Users can create company messages" ON public.whatsapp_messages;

-- Create comprehensive RLS policies for whatsapp_conversations
-- These policies support SELECT, INSERT, UPDATE, and DELETE operations

CREATE POLICY "WhatsApp conversations - company access" ON public.whatsapp_conversations
  FOR ALL USING (
    -- Super admins can access all conversations
    public.is_super_admin((SELECT auth.uid())) OR
    -- Company users can access conversations in their company
    company_id = public.get_user_company_id((SELECT auth.uid()))
  );

-- Create comprehensive RLS policies for whatsapp_messages
-- Messages are accessed through their conversation's company relationship

CREATE POLICY "WhatsApp messages - company access" ON public.whatsapp_messages
  FOR ALL USING (
    -- Super admins can access all messages
    public.is_super_admin((SELECT auth.uid())) OR
    -- Company users can access messages in conversations from their company
    EXISTS (
      SELECT 1 FROM public.whatsapp_conversations 
      WHERE id = conversation_id 
      AND company_id = public.get_user_company_id((SELECT auth.uid()))
    )
  );

-- Add performance indexes to support the optimized policies
CREATE INDEX IF NOT EXISTS idx_whatsapp_conversations_company_auth 
  ON public.whatsapp_conversations(company_id) 
  WHERE company_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_conversation_lookup 
  ON public.whatsapp_messages(conversation_id);

-- Ensure CASCADE deletion is properly set up
-- This ensures that when a conversation is deleted, all its messages are automatically deleted
ALTER TABLE public.whatsapp_messages 
DROP CONSTRAINT IF EXISTS whatsapp_messages_conversation_id_fkey;

ALTER TABLE public.whatsapp_messages 
ADD CONSTRAINT whatsapp_messages_conversation_id_fkey 
FOREIGN KEY (conversation_id) 
REFERENCES public.whatsapp_conversations(id) 
ON DELETE CASCADE;

-- Create a function to safely delete conversations with all related data
CREATE OR REPLACE FUNCTION delete_whatsapp_conversation(conversation_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_company_id UUID;
  conversation_company_id UUID;
BEGIN
  -- Get the user's company ID
  user_company_id := public.get_user_company_id((SELECT auth.uid()));
  
  -- Check if user is super admin
  IF public.is_super_admin((SELECT auth.uid())) THEN
    -- Super admin can delete any conversation
    DELETE FROM public.whatsapp_conversations WHERE id = conversation_uuid;
    RETURN FOUND;
  END IF;
  
  -- Get the conversation's company ID
  SELECT company_id INTO conversation_company_id 
  FROM public.whatsapp_conversations 
  WHERE id = conversation_uuid;
  
  -- Check if conversation exists and belongs to user's company
  IF conversation_company_id IS NULL THEN
    RETURN FALSE; -- Conversation not found
  END IF;
  
  IF conversation_company_id != user_company_id THEN
    RETURN FALSE; -- User doesn't have permission
  END IF;
  
  -- Delete the conversation (messages will be deleted automatically due to CASCADE)
  DELETE FROM public.whatsapp_conversations WHERE id = conversation_uuid;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION delete_whatsapp_conversation(UUID) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION delete_whatsapp_conversation(UUID) IS 'Safely delete a WhatsApp conversation with proper permission checks';

-- Log the completion
DO $$
BEGIN
    RAISE NOTICE 'WhatsApp RLS policies and deletion functionality fixed successfully';
END $$;
