-- Fix Company Cascade Deletion
-- This migration adds missing foreign key constraints with ON DELETE CASCADE
-- to ensure proper cascade deletion when companies are deleted

-- Add foreign key constraint for case_types.company_id
ALTER TABLE public.case_types 
DROP CONSTRAINT IF EXISTS case_types_company_id_fkey;

ALTER TABLE public.case_types 
ADD CONSTRAINT case_types_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for cases.company_id
ALTER TABLE public.cases 
DROP CONSTRAINT IF EXISTS cases_company_id_fkey;

ALTER TABLE public.cases 
ADD CONSTRAINT cases_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for case_tasks.company_id
ALTER TABLE public.case_tasks 
DROP CONSTRAINT IF EXISTS case_tasks_company_id_fkey;

ALTER TABLE public.case_tasks 
ADD CONSTRAINT case_tasks_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for case_time_entries.company_id
ALTER TABLE public.case_time_entries 
DROP CONSTRAINT IF EXISTS case_time_entries_company_id_fkey;

ALTER TABLE public.case_time_entries 
ADD CONSTRAINT case_time_entries_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for case_documents.company_id
ALTER TABLE public.case_documents 
DROP CONSTRAINT IF EXISTS case_documents_company_id_fkey;

ALTER TABLE public.case_documents 
ADD CONSTRAINT case_documents_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for whatsapp_conversations.company_id
ALTER TABLE public.whatsapp_conversations 
DROP CONSTRAINT IF EXISTS whatsapp_conversations_company_id_fkey;

ALTER TABLE public.whatsapp_conversations 
ADD CONSTRAINT whatsapp_conversations_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for task_reminders.company_id
ALTER TABLE public.task_reminders 
DROP CONSTRAINT IF EXISTS task_reminders_company_id_fkey;

ALTER TABLE public.task_reminders 
ADD CONSTRAINT task_reminders_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Note: Company deletion with auth user cleanup will be handled by Edge Function
-- The foreign key constraints above will ensure proper cascade deletion of database records

-- Create indexes to improve deletion performance
CREATE INDEX IF NOT EXISTS idx_case_types_company_cascade ON public.case_types(company_id);
CREATE INDEX IF NOT EXISTS idx_cases_company_cascade ON public.cases(company_id);
CREATE INDEX IF NOT EXISTS idx_case_tasks_company_cascade ON public.case_tasks(company_id);
CREATE INDEX IF NOT EXISTS idx_case_time_entries_company_cascade ON public.case_time_entries(company_id);
CREATE INDEX IF NOT EXISTS idx_case_documents_company_cascade ON public.case_documents(company_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_conversations_company_cascade ON public.whatsapp_conversations(company_id);
CREATE INDEX IF NOT EXISTS idx_task_reminders_company_cascade ON public.task_reminders(company_id);

-- Add comments to document the constraints
COMMENT ON CONSTRAINT case_types_company_id_fkey ON public.case_types IS 'Ensures case types are deleted when company is deleted';
COMMENT ON CONSTRAINT cases_company_id_fkey ON public.cases IS 'Ensures cases are deleted when company is deleted';
COMMENT ON CONSTRAINT case_tasks_company_id_fkey ON public.case_tasks IS 'Ensures case tasks are deleted when company is deleted';
COMMENT ON CONSTRAINT case_time_entries_company_id_fkey ON public.case_time_entries IS 'Ensures time entries are deleted when company is deleted';
COMMENT ON CONSTRAINT case_documents_company_id_fkey ON public.case_documents IS 'Ensures case documents are deleted when company is deleted';
COMMENT ON CONSTRAINT whatsapp_conversations_company_id_fkey ON public.whatsapp_conversations IS 'Ensures WhatsApp conversations are deleted when company is deleted';
COMMENT ON CONSTRAINT task_reminders_company_id_fkey ON public.task_reminders IS 'Ensures task reminders are deleted when company is deleted';
