-- Optimize RLS policies for case_time_entries table (4 policies)
-- This table is critical for time tracking performance
-- Addresses auth_rls_initplan performance issues

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view company case time entries" ON public.case_time_entries;
DROP POLICY IF EXISTS "Users can create company case time entries" ON public.case_time_entries;
DROP POLICY IF EXISTS "Users can update company case time entries" ON public.case_time_entries;
DROP POLICY IF EXISTS "Users can delete company case time entries" ON public.case_time_entries;

-- Create optimized policies using (SELECT auth.uid()) pattern

-- SELECT policy - users can view time entries from their company
CREATE POLICY "Users can view company case time entries" ON public.case_time_entries
  FOR SELECT USING (
    (EXISTS ( 
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = (SELECT auth.uid()) 
      AND user_roles.role = 'super_admin'::app_role
    )) OR 
    (company_id = public.get_user_company())
  );

-- INSERT policy - users can create time entries for their company
CREATE POLICY "Users can create company case time entries" ON public.case_time_entries
  FOR INSERT WITH CHECK (
    (EXISTS ( 
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = (SELECT auth.uid()) 
      AND user_roles.role = 'super_admin'::app_role
    )) OR 
    (company_id = public.get_user_company())
  );

-- UPDATE policy - users can update time entries from their company
CREATE POLICY "Users can update company case time entries" ON public.case_time_entries
  FOR UPDATE USING (
    (EXISTS ( 
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = (SELECT auth.uid()) 
      AND user_roles.role = 'super_admin'::app_role
    )) OR 
    (company_id = public.get_user_company())
  );

-- DELETE policy - users can delete time entries from their company
CREATE POLICY "Users can delete company case time entries" ON public.case_time_entries
  FOR DELETE USING (
    (EXISTS ( 
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = (SELECT auth.uid()) 
      AND user_roles.role = 'super_admin'::app_role
    )) OR 
    (company_id = public.get_user_company())
  );

-- Performance testing for time tracking queries
SELECT 
  'Time Entries Performance Test - Post Optimization' as test_name,
  COUNT(*) as record_count,
  COALESCE(SUM(duration), 0) as total_duration,
  EXTRACT(EPOCH FROM (clock_timestamp() - statement_timestamp())) * 1000 as execution_time_ms
FROM case_time_entries 
WHERE created_at >= NOW() - INTERVAL '7 days';

-- Test time entries with case join (common pattern)
SELECT 
  'Time Entries Join Performance Test - Post Optimization' as test_name,
  COUNT(*) as record_count,
  EXTRACT(EPOCH FROM (clock_timestamp() - statement_timestamp())) * 1000 as execution_time_ms
FROM case_time_entries cte
JOIN cases c ON cte.case_id = c.id
WHERE cte.created_at >= NOW() - INTERVAL '7 days';

-- Add comments for documentation
COMMENT ON POLICY "Users can view company case time entries" ON public.case_time_entries 
IS 'Optimized RLS policy using (SELECT auth.uid()) for better performance - addresses auth_rls_initplan issue';

-- Verify policies are active
SELECT 
  'Case Time Entries RLS Policies Verification' as test_name,
  COUNT(*) as policy_count
FROM pg_policies 
WHERE tablename = 'case_time_entries' AND schemaname = 'public';
