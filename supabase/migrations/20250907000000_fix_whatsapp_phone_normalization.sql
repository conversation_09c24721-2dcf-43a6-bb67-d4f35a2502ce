-- Fix WhatsApp conversation phone number normalization
-- This migration normalizes phone numbers in existing conversations to ensure consistency

-- Create a function to normalize phone numbers
CREATE OR REPLACE FUNCTION normalize_phone_number(phone_input TEXT)
RETURNS TEXT AS $$
DECLARE
    digits_only TEXT;
BEGIN
    -- Remove any non-digit characters
    digits_only := regexp_replace(phone_input, '[^0-9]', '', 'g');
    
    -- Convert to international format (972...) 
    IF digits_only ~ '^0' AND length(digits_only) = 10 THEN
        -- Israeli local format (0xxxxxxxxx) -> international (972xxxxxxxxx)
        RETURN '972' || substring(digits_only from 2);
    END IF;
    
    -- If already starts with 972, keep it
    IF digits_only ~ '^972' THEN
        RETURN digits_only;
    END IF;
    
    -- If 9 digits, assume Israeli mobile and add 972
    IF length(digits_only) = 9 THEN
        RETURN '972' || digits_only;
    END IF;
    
    RETURN digits_only;
END;
$$ LANGUAGE plpgsql;

-- Update existing conversations to use normalized phone numbers
UPDATE whatsapp_conversations 
SET phone_number = normalize_phone_number(phone_number)
WHERE phone_number != normalize_phone_number(phone_number);

-- Find and merge duplicate conversations that may have been created due to phone format inconsistencies
WITH duplicate_conversations AS (
    SELECT 
        phone_number,
        company_id,
        array_agg(id ORDER BY created_at) as conversation_ids,
        count(*) as conversation_count
    FROM whatsapp_conversations 
    GROUP BY phone_number, company_id
    HAVING count(*) > 1
),
conversations_to_keep AS (
    SELECT 
        phone_number,
        company_id,
        conversation_ids[1] as keep_id,
        conversation_ids[2:] as merge_ids
    FROM duplicate_conversations
)
-- Update messages to point to the conversation we're keeping
UPDATE whatsapp_messages 
SET conversation_id = ctk.keep_id
FROM conversations_to_keep ctk
WHERE conversation_id = ANY(ctk.merge_ids);

-- Delete duplicate conversations (keeping the oldest one)
WITH duplicate_conversations AS (
    SELECT 
        phone_number,
        company_id,
        array_agg(id ORDER BY created_at) as conversation_ids,
        count(*) as conversation_count
    FROM whatsapp_conversations 
    GROUP BY phone_number, company_id
    HAVING count(*) > 1
),
conversations_to_delete AS (
    SELECT 
        unnest(conversation_ids[2:]) as delete_id
    FROM duplicate_conversations
)
DELETE FROM whatsapp_conversations 
WHERE id IN (SELECT delete_id FROM conversations_to_delete);

-- Add a comment to track this migration
COMMENT ON FUNCTION normalize_phone_number(TEXT) IS 'Normalizes phone numbers to ensure consistent formatting across WhatsApp functions';

-- Log the completion
DO $$
BEGIN
    RAISE NOTICE 'WhatsApp phone number normalization migration completed successfully';
END $$;
