import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

interface WorkflowRequest {
  action: 'create' | 'update' | 'delete' | 'list' | 'get' | 'toggle';
  workflowId?: string;
  workflowData?: {
    name: string;
    description?: string;
    trigger_type: 'lead_status_change' | 'case_status_change' | 'lead_answer_status_change' | 'manual';
    trigger_config: {
      from_status?: string;
      to_status?: string;
      target_statuses?: string[];
      description?: string;
    };
    steps: Array<{
      step_type: 'send_whatsapp' | 'wait' | 'update_lead_status' | 'update_case_status' | 'create_case';
      step_config: any;
    }>;
  };
  companyId?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get user from auth header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      throw new Error('Invalid authentication');
    }

    // Get user's role and determine target company
    const { data: userRole, error: roleError } = await supabaseAdmin
      .from('user_roles')
      .select('company_id, role')
      .eq('user_id', user.id)
      .single();

    if (roleError || !userRole) {
      throw new Error('User role not found');
    }

    const body: WorkflowRequest = await req.json();
    const { action } = body;

    // For super admins, use the provided companyId from the current session
    // For regular users, use their assigned company_id
    const targetCompanyId = userRole.role === 'super_admin' ? body.companyId : userRole.company_id;

    if (!targetCompanyId) {
      throw new Error('No company context available');
    }

    switch (action) {
      case 'list':
        return await listWorkflows(supabaseAdmin, targetCompanyId);

      case 'get':
        if (!body.workflowId) {
          throw new Error('Workflow ID required');
        }
        return await getWorkflow(supabaseAdmin, body.workflowId, targetCompanyId);

      case 'create':
        if (!body.workflowData) {
          throw new Error('Workflow data required');
        }
        return await createWorkflow(supabaseAdmin, body.workflowData, targetCompanyId, user.id);

      case 'update':
        if (!body.workflowId || !body.workflowData) {
          throw new Error('Workflow ID and data required');
        }
        return await updateWorkflow(supabaseAdmin, body.workflowId, body.workflowData, targetCompanyId);

      case 'delete':
        if (!body.workflowId) {
          throw new Error('Workflow ID required');
        }
        return await deleteWorkflow(supabaseAdmin, body.workflowId, targetCompanyId);

      case 'toggle':
        if (!body.workflowId) {
          throw new Error('Workflow ID required');
        }
        return await toggleWorkflow(supabaseAdmin, body.workflowId, targetCompanyId);
      
      default:
        throw new Error('Invalid action');
    }

  } catch (error) {
    console.error('Error in workflow-management:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

async function listWorkflows(supabaseAdmin: any, companyId: string) {
  const { data: workflows, error } = await supabaseAdmin
    .from('workflows')
    .select(`
      *,
      workflow_steps (
        id,
        step_order,
        step_type,
        step_config
      )
    `)
    .eq('company_id', companyId)
    .order('created_at', { ascending: false });

  if (error) throw error;

  return new Response(
    JSON.stringify({ success: true, workflows }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function getWorkflow(supabaseAdmin: any, workflowId: string, companyId: string) {
  const { data: workflow, error } = await supabaseAdmin
    .from('workflows')
    .select(`
      *,
      workflow_steps (
        id,
        step_order,
        step_type,
        step_config
      )
    `)
    .eq('id', workflowId)
    .eq('company_id', companyId)
    .single();

  if (error) throw error;

  return new Response(
    JSON.stringify({ success: true, workflow }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function createWorkflow(supabaseAdmin: any, workflowData: any, companyId: string, userId: string) {
  // Start transaction
  const { data: workflow, error: workflowError } = await supabaseAdmin
    .from('workflows')
    .insert({
      company_id: companyId,
      name: workflowData.name,
      description: workflowData.description,
      trigger_type: workflowData.trigger_type,
      trigger_config: workflowData.trigger_config,
      created_by: userId
    })
    .select()
    .single();

  if (workflowError) throw workflowError;

  // Create workflow steps
  if (workflowData.steps && workflowData.steps.length > 0) {
    const steps = workflowData.steps.map((step: any, index: number) => ({
      workflow_id: workflow.id,
      step_order: index + 1,
      step_type: step.step_type,
      step_config: step.step_config
    }));

    const { error: stepsError } = await supabaseAdmin
      .from('workflow_steps')
      .insert(steps);

    if (stepsError) throw stepsError;
  }

  return new Response(
    JSON.stringify({ success: true, workflow }),
    { status: 201, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function updateWorkflow(supabaseAdmin: any, workflowId: string, workflowData: any, companyId: string) {
  // Update workflow
  const { data: workflow, error: workflowError } = await supabaseAdmin
    .from('workflows')
    .update({
      name: workflowData.name,
      description: workflowData.description,
      trigger_type: workflowData.trigger_type,
      trigger_config: workflowData.trigger_config
    })
    .eq('id', workflowId)
    .eq('company_id', companyId)
    .select()
    .single();

  if (workflowError) throw workflowError;

  // Delete existing steps
  const { error: deleteError } = await supabaseAdmin
    .from('workflow_steps')
    .delete()
    .eq('workflow_id', workflowId);

  if (deleteError) throw deleteError;

  // Create new steps
  if (workflowData.steps && workflowData.steps.length > 0) {
    const steps = workflowData.steps.map((step: any, index: number) => ({
      workflow_id: workflowId,
      step_order: index + 1,
      step_type: step.step_type,
      step_config: step.step_config
    }));

    const { error: stepsError } = await supabaseAdmin
      .from('workflow_steps')
      .insert(steps);

    if (stepsError) throw stepsError;
  }

  return new Response(
    JSON.stringify({ success: true, workflow }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function deleteWorkflow(supabaseAdmin: any, workflowId: string, companyId: string) {
  const { error } = await supabaseAdmin
    .from('workflows')
    .delete()
    .eq('id', workflowId)
    .eq('company_id', companyId);

  if (error) throw error;

  return new Response(
    JSON.stringify({ success: true }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function toggleWorkflow(supabaseAdmin: any, workflowId: string, companyId: string) {
  // Get current status
  const { data: workflow, error: getError } = await supabaseAdmin
    .from('workflows')
    .select('is_active')
    .eq('id', workflowId)
    .eq('company_id', companyId)
    .single();

  if (getError) throw getError;

  // Toggle status
  const { data: updatedWorkflow, error: updateError } = await supabaseAdmin
    .from('workflows')
    .update({ is_active: !workflow.is_active })
    .eq('id', workflowId)
    .eq('company_id', companyId)
    .select()
    .single();

  if (updateError) throw updateError;

  return new Response(
    JSON.stringify({ success: true, workflow: updatedWorkflow }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

serve(handler);
