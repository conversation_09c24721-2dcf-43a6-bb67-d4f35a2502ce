import { createClient } from 'npm:@supabase/supabase-js@2';
import { isGroupChat } from '../_shared/phone-utils.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface CleanupResult {
  conversationsDeleted: number;
  messagesDeleted: number;
  groupChatsFound: string[];
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Get user from auth header
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Invalid authorization');
    }

    // Check if user is super admin (only super admins can run cleanup)
    const { data: userRole, error: roleError } = await supabaseClient
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (roleError || !userRole || userRole.role !== 'super_admin') {
      throw new Error('Only super admins can run WhatsApp group cleanup');
    }

    console.log('Starting WhatsApp group cleanup...');

    // Find all conversations that are group chats (green_api_chat_id ends with @g.us)
    const { data: groupConversations, error: fetchError } = await supabaseClient
      .from('whatsapp_conversations')
      .select('id, green_api_chat_id, contact_name, company_id')
      .not('green_api_chat_id', 'is', null);

    if (fetchError) {
      throw new Error(`Failed to fetch conversations: ${fetchError.message}`);
    }

    // Filter for group chats
    const groupChats = groupConversations?.filter(conv => 
      conv.green_api_chat_id && isGroupChat(conv.green_api_chat_id)
    ) || [];

    console.log(`Found ${groupChats.length} group conversations to delete`);

    let messagesDeleted = 0;
    let conversationsDeleted = 0;
    const groupChatsFound: string[] = [];

    // Delete group conversations and their messages
    for (const groupChat of groupChats) {
      try {
        console.log(`Deleting group chat: ${groupChat.green_api_chat_id} (${groupChat.contact_name})`);
        
        // Count messages before deletion
        const { count: messageCount } = await supabaseClient
          .from('whatsapp_messages')
          .select('*', { count: 'exact', head: true })
          .eq('conversation_id', groupChat.id);

        // Delete the conversation (messages will be deleted automatically due to CASCADE)
        const { error: deleteError } = await supabaseClient
          .from('whatsapp_conversations')
          .delete()
          .eq('id', groupChat.id);

        if (deleteError) {
          console.error(`Failed to delete conversation ${groupChat.id}:`, deleteError);
          continue;
        }

        conversationsDeleted++;
        messagesDeleted += messageCount || 0;
        groupChatsFound.push(groupChat.green_api_chat_id);

        console.log(`Deleted group chat ${groupChat.green_api_chat_id} with ${messageCount || 0} messages`);
      } catch (error) {
        console.error(`Error deleting group chat ${groupChat.id}:`, error);
      }
    }

    const result: CleanupResult = {
      conversationsDeleted,
      messagesDeleted,
      groupChatsFound
    };

    console.log('WhatsApp group cleanup completed:', result);

    return new Response(JSON.stringify({
      success: true,
      result
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in cleanup-whatsapp-groups:', error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
};

Deno.serve(handler);
