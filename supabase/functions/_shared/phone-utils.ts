/**
 * Shared utility functions for phone number normalization across WhatsApp functions
 * This ensures consistent phone number formatting between outgoing and incoming messages
 */

/**
 * Normalize phone number to ensure consistent formatting across all WhatsApp functions
 * @param phoneNumber - Phone number in any format
 * @returns Normalized phone number in 972XXXXXXXXX format
 */
export const normalizePhoneNumber = (phoneNumber: string): string => {
  // Remove any non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');

  // Convert to international format (972...) to match webhook function behavior
  if (digitsOnly.startsWith('0') && digitsOnly.length === 10) {
    // Israeli local format (0xxxxxxxxx) -> international (972xxxxxxxxx)
    return '972' + digitsOnly.substring(1);
  }

  // If already starts with 972, keep it
  if (digitsOnly.startsWith('972')) {
    return digitsOnly;
  }

  // If 9 digits, assume Israeli mobile and add 972
  if (digitsOnly.length === 9) {
    return '972' + digitsOnly;
  }

  return digitsOnly;
};

/**
 * Format phone number for WhatsApp chat ID
 * @param phoneNumber - Phone number in any format
 * @returns Chat ID <NAME_EMAIL>
 */
export const formatChatId = (phoneNumber: string): string => {
  const normalized = normalizePhoneNumber(phoneNumber);
  return `${normalized}@c.us`;
};

/**
 * Extract phone number from WhatsApp chat ID
 * @param chatId - Chat ID <NAME_EMAIL>
 * @returns Normalized phone number
 */
export const extractPhoneFromChatId = (chatId: string): string => {
  const phone = chatId.replace('@c.us', '');
  return normalizePhoneNumber(phone);
};

/**
 * Check if a WhatsApp chat ID represents a group chat
 * @param chatId - Chat ID to check
 * @returns True if the chat ID is a group chat (ends with @g.us)
 */
export const isGroupChat = (chatId: string): boolean => {
  return chatId.endsWith('@g.us');
};

/**
 * Check if a WhatsApp chat ID represents a private chat
 * @param chatId - Chat ID to check
 * @returns True if the chat ID is a private chat (ends with @c.us)
 */
export const isPrivateChat = (chatId: string): boolean => {
  return chatId.endsWith('@c.us');
};
