import { createClient } from 'npm:@supabase/supabase-js@2';
import { normalizePhoneNumber, extractPhoneFromChatId, isGroupChat } from '../_shared/phone-utils.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Helper function to safely convert timestamp
const safeTimestampToISO = (timestamp: number): string => {
  try {
    // Validate timestamp is a reasonable value (not too old, not too far in future)
    const now = Date.now() / 1000;
    const oneYearAgo = now - (365 * 24 * 60 * 60);
    const oneYearFromNow = now + (365 * 24 * 60 * 60);
    
    if (timestamp < oneYearAgo || timestamp > oneYearFromNow) {
      console.warn(`Invalid timestamp ${timestamp}, using current time`);
      return new Date().toISOString();
    }
    
    const date = new Date(timestamp * 1000);
    if (isNaN(date.getTime())) {
      console.warn(`Invalid date from timestamp ${timestamp}, using current time`);
      return new Date().toISOString();
    }
    
    return date.toISOString();
  } catch (error) {
    console.error(`Error converting timestamp ${timestamp}:`, error);
    return new Date().toISOString();
  }
};

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const webhook = await req.json();
    console.log('Received GreenAPI webhook:', JSON.stringify(webhook, null, 2));

    // Handle incoming message webhook
    if (webhook.typeWebhook === 'incomingMessageReceived') {
      const messageData = webhook.messageData;
      const senderData = webhook.senderData;

      // Skip group messages - we only handle private conversations
      if (isGroupChat(senderData.chatId)) {
        console.log(`Ignoring group message from: ${senderData.chatId}`);
        return new Response(JSON.stringify({ success: true }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      if (messageData.typeMessage !== 'textMessage') {
        console.log('Ignoring non-text message');
        return new Response(JSON.stringify({ success: true }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Extract and normalize phone number from chatId (remove @c.us suffix)
      const rawPhoneNumber = senderData.chatId.replace('@c.us', '');
      const normalizedPhoneNumber = extractPhoneFromChatId(senderData.chatId);
      console.log(`Raw phone: ${rawPhoneNumber}, Normalized: ${normalizedPhoneNumber}`);
      
      // Find which company this instance belongs to
      const instanceId = new URL(req.url).searchParams.get('instanceId');
      if (!instanceId) {
        throw new Error('Instance ID not provided in webhook URL');
      }

      const { data: company } = await supabaseClient
        .from('companies')
        .select('id, name')
        .eq('green_api_instance_id', instanceId)
        .single();

      if (!company) {
        console.log(`No company found for instance ID: ${instanceId}`);
        return new Response(JSON.stringify({ success: true }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Find or create conversation
      let { data: conversation } = await supabaseClient
        .from('whatsapp_conversations')
        .select('id, unread_count')
        .eq('company_id', company.id)
        .eq('phone_number', normalizedPhoneNumber)
        .single();

      if (!conversation) {
        // Try to find matching lead by phone number (try both normalized and raw formats)
        let { data: lead } = await supabaseClient
          .from('leads')
          .select('id, full_name, phone')
          .eq('company_id', company.id)
          .eq('phone', normalizedPhoneNumber)
          .single();

        // If not found with normalized number, try with raw number
        if (!lead) {
          const { data: leadWithRaw } = await supabaseClient
            .from('leads')
            .select('id, full_name, phone')
            .eq('company_id', company.id)
            .eq('phone', rawPhoneNumber)
            .single();
          lead = leadWithRaw;
        }

        console.log(`Lead search result:`, lead ? `Found lead ${lead.full_name} with phone ${lead.phone}` : 'No matching lead found');

        const { data: newConversation, error: insertError } = await supabaseClient
          .from('whatsapp_conversations')
          .insert({
            company_id: company.id,
            lead_id: lead?.id || null,
            phone_number: normalizedPhoneNumber,
            green_api_chat_id: senderData.chatId,
            contact_name: lead?.full_name || senderData.senderName || normalizedPhoneNumber,
            last_message: messageData.textMessageData.textMessage,
            last_message_timestamp: safeTimestampToISO(webhook.timestamp),
            unread_count: 1,
          })
          .select('id, unread_count')
          .single();

        if (insertError) {
          console.error('Error creating conversation:', insertError);
          throw new Error(`Failed to create conversation: ${insertError.message}`);
        }

        conversation = newConversation;
      } else {
        // Update conversation with new message and increment unread count
        await supabaseClient
          .from('whatsapp_conversations')
          .update({
            last_message: messageData.textMessageData.textMessage,
            last_message_timestamp: safeTimestampToISO(webhook.timestamp),
            unread_count: conversation.unread_count + 1,
          })
          .eq('id', conversation.id);
      }

      // Store the incoming message
      if (!conversation?.id) {
        throw new Error('Conversation ID is missing - cannot store message');
      }

      await supabaseClient
        .from('whatsapp_messages')
        .insert({
          conversation_id: conversation.id,
          green_api_message_id: webhook.idMessage,
          content: messageData.textMessageData.textMessage,
          message_type: 'text',
          sender_type: 'incoming',
          status: 'received',
        });

      console.log(`Message stored for conversation ${conversation.id}`);
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in green-api-webhook:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
};

Deno.serve(handler);