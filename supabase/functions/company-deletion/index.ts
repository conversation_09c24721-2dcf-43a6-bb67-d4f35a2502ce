import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.56.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface DeleteCompanyRequest {
  companyId: string;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Check environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    console.log('Environment check:', {
      hasUrl: !!supabaseUrl,
      hasServiceKey: !!supabaseServiceKey,
      urlPrefix: supabaseUrl?.substring(0, 20) + '...'
    })

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing environment variables')
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client with service role key for admin operations
    const supabaseAdmin = createClient(
      supabaseUrl,
      supabaseServiceKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    console.log('Auth header present:', !!authHeader)

    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify the user is authenticated
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    console.log('User authentication:', {
      hasUser: !!user,
      userId: user?.id,
      authError: authError?.message
    })

    if (authError || !user) {
      return new Response(
        JSON.stringify({
          error: 'Invalid or expired token',
          details: authError?.message
        }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if user is super admin - use a more flexible query
    const { data: userRoles, error: roleError } = await supabaseAdmin
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)

    console.log('User roles check:', {
      roles: userRoles,
      roleError: roleError?.message
    })

    if (roleError) {
      return new Response(
        JSON.stringify({
          error: 'Failed to check user permissions',
          details: roleError.message
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const isSuperAdmin = userRoles?.some(role => role.role === 'super_admin')

    if (!isSuperAdmin) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized: Only super admins can delete companies' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { companyId }: DeleteCompanyRequest = await req.json()

    if (!companyId) {
      return new Response(
        JSON.stringify({ error: 'Company ID is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Starting company deletion process for company: ${companyId}`)

    // Step 1: Verify company exists
    const { data: company, error: companyError } = await supabaseAdmin
      .from('companies')
      .select('id, name')
      .eq('id', companyId)
      .single()

    if (companyError || !company) {
      return new Response(
        JSON.stringify({ error: 'Company not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Found company: ${company.name} (${company.id})`)

    // Step 2: Get all users associated with this company
    const { data: companyUsers, error: usersError } = await supabaseAdmin
      .from('user_roles')
      .select('user_id')
      .eq('company_id', companyId)

    if (usersError) {
      console.error('Error fetching company users:', usersError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch company users' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Found ${companyUsers?.length || 0} users to delete`)

    // Step 3: Delete all files from storage buckets for this company
    try {
      // Get all case documents for this company
      const { data: caseDocuments, error: documentsError } = await supabaseAdmin
        .from('case_documents')
        .select('file_path')
        .eq('company_id', companyId)

      if (!documentsError && caseDocuments) {
        console.log(`Found ${caseDocuments.length} case documents to delete`)
        
        // Delete each file from storage
        for (const doc of caseDocuments) {
          const { error: storageError } = await supabaseAdmin.storage
            .from('case-documents')
            .remove([doc.file_path])
          
          if (storageError) {
            console.error(`Error deleting file ${doc.file_path}:`, storageError)
          } else {
            console.log(`Deleted file: ${doc.file_path}`)
          }
        }
      }
    } catch (error) {
      console.error('Error cleaning up storage files:', error)
      // Continue with deletion even if storage cleanup fails
    }

    // Step 4: Manual deletion of related data (since CASCADE constraints may not be in place)
    console.log('Starting manual deletion of related data...')

    try {
      // Delete in reverse dependency order to avoid constraint violations

      // Delete workflow execution logs
      await supabaseAdmin.from('workflow_execution_logs').delete().eq('execution_id',
        supabaseAdmin.from('workflow_executions').select('id').eq('company_id', companyId)
      )

      // Delete workflow executions
      await supabaseAdmin.from('workflow_executions').delete().eq('company_id', companyId)

      // Delete workflow steps
      await supabaseAdmin.from('workflow_steps').delete().eq('workflow_id',
        supabaseAdmin.from('workflows').select('id').eq('company_id', companyId)
      )

      // Delete workflows
      await supabaseAdmin.from('workflows').delete().eq('company_id', companyId)

      // Delete workflow triggers
      await supabaseAdmin.from('workflow_triggers').delete().eq('company_id', companyId)

      // Delete task reminders
      await supabaseAdmin.from('task_reminders').delete().eq('company_id', companyId)

      // Delete case time entries
      await supabaseAdmin.from('case_time_entries').delete().eq('company_id', companyId)

      // Delete case documents
      await supabaseAdmin.from('case_documents').delete().eq('company_id', companyId)

      // Delete case tasks
      await supabaseAdmin.from('case_tasks').delete().eq('company_id', companyId)

      // Delete cases
      await supabaseAdmin.from('cases').delete().eq('company_id', companyId)

      // Delete case types
      await supabaseAdmin.from('case_types').delete().eq('company_id', companyId)

      // Delete WhatsApp messages (via conversations)
      const { data: conversations } = await supabaseAdmin
        .from('whatsapp_conversations')
        .select('id')
        .eq('company_id', companyId)

      if (conversations && conversations.length > 0) {
        for (const conv of conversations) {
          await supabaseAdmin.from('whatsapp_messages').delete().eq('conversation_id', conv.id)
        }
      }

      // Delete WhatsApp conversations
      await supabaseAdmin.from('whatsapp_conversations').delete().eq('company_id', companyId)

      // Delete lead answer status
      await supabaseAdmin.from('lead_answer_status').delete().eq('company_id', companyId)

      // Delete lead activities
      await supabaseAdmin.from('lead_activities').delete().eq('company_id', companyId)

      // Delete leads
      await supabaseAdmin.from('leads').delete().eq('company_id', companyId)

      // Delete user invitations
      await supabaseAdmin.from('user_invitations').delete().eq('company_id', companyId)

      console.log('Completed manual deletion of related data')

    } catch (error) {
      console.error('Error during manual deletion:', error)
      // Continue with user deletion even if some data cleanup fails
    }

    // Step 5: Delete all users from Supabase Auth
    let deletedUsersCount = 0
    let failedUsersCount = 0

    if (companyUsers && companyUsers.length > 0) {
      console.log(`Attempting to delete ${companyUsers.length} users from auth...`)

      for (const userRole of companyUsers) {
        try {
          // First verify the user exists in auth
          const { data: authUser, error: getUserError } = await supabaseAdmin.auth.admin.getUserById(
            userRole.user_id
          )

          if (getUserError) {
            console.log(`User ${userRole.user_id} not found in auth (may already be deleted):`, getUserError.message)
            continue
          }

          console.log(`Deleting auth user: ${authUser.user?.email} (${userRole.user_id})`)

          // Delete the user from auth
          const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(
            userRole.user_id
          )

          if (deleteUserError) {
            console.error(`Error deleting user ${userRole.user_id}:`, deleteUserError)
            failedUsersCount++
          } else {
            console.log(`Successfully deleted user from auth: ${userRole.user_id}`)
            deletedUsersCount++
          }
        } catch (error) {
          console.error(`Failed to delete user ${userRole.user_id}:`, error)
          failedUsersCount++
          // Continue with other users even if one fails
        }
      }

      console.log(`Auth user deletion summary: ${deletedUsersCount} deleted, ${failedUsersCount} failed`)
    } else {
      console.log('No users found to delete from auth')
    }

    // Step 6: Delete the company (should work now that related data is cleaned up)
    const { error: deleteCompanyError } = await supabaseAdmin
      .from('companies')
      .delete()
      .eq('id', companyId)

    if (deleteCompanyError) {
      console.error('Error deleting company:', deleteCompanyError)
      return new Response(
        JSON.stringify({
          error: 'Failed to delete company',
          details: deleteCompanyError.message
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Successfully deleted company: ${company.name} (${companyId})`)

    return new Response(
      JSON.stringify({
        success: true,
        message: `Company "${company.name}" and all associated data deleted successfully`,
        totalUsers: companyUsers?.length || 0,
        deletedUsers: deletedUsersCount,
        failedUsers: failedUsersCount
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Unexpected error in company deletion:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
