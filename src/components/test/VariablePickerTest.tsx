import { useState, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { VariablePicker } from '../marketing/VariablePicker';

/**
 * Test component to verify the enhanced VariablePicker functionality
 * This component demonstrates the click-to-insert variable functionality
 */
export const VariablePickerTest = () => {
  const [message, setMessage] = useState('שלום {lead_name}, אנחנו מ{company_name} ונשמח לעזור לך!');
  const [cursorPosition, setCursorPosition] = useState<number | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleVariableSelect = (variable: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;
    
    const currentMessage = message || '';
    const insertPosition = cursorPosition ?? currentMessage.length;
    
    const newMessage = 
      currentMessage.slice(0, insertPosition) + 
      variable + 
      currentMessage.slice(insertPosition);
      
    setMessage(newMessage);
    
    // Update cursor position to end of inserted variable
    setTimeout(() => {
      const newPosition = insertPosition + variable.length;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  const handleTextareaClick = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      setCursorPosition(textarea.selectionStart);
    }
  };

  const handleTextareaKeyUp = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      setCursorPosition(textarea.selectionStart);
    }
  };

  const handleTest = () => {
    console.log('Test Message:', message);
    alert(`הודעת הבדיקה:\n\n${message}\n\nמיקום הסמן: ${cursorPosition}`);
  };

  const handleClear = () => {
    setMessage('');
    setCursorPosition(0);
  };

  const presetMessages = [
    'שלום {lead_name}, תודה שפנית אלינו!',
    'היי {lead_name}, אני {assigned_user_name} מ{company_name}',
    'ליד חדש: {lead_name} ({lead_phone}) - ערך: {lead_value}',
    'פרטי החברה: {company_name}, {company_email}, {company_address}'
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>בדיקת VariablePicker - משתנים משופרים</CardTitle>
          <p className="text-sm text-muted-foreground">
            בדוק את הפונקציונליות החדשה של הוספת משתנים בלחיצה
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          
          {/* Quick Preset Messages */}
          <div>
            <Label className="text-sm font-medium">הודעות מוכנות לבדיקה:</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {presetMessages.map((preset, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setMessage(preset)}
                  className="text-xs"
                >
                  דוגמה {index + 1}
                </Button>
              ))}
            </div>
          </div>

          {/* Main Test Interface */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            {/* Message Input */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="test-message">הודעת בדיקה</Label>
                <Textarea
                  ref={textareaRef}
                  id="test-message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onClick={handleTextareaClick}
                  onKeyUp={handleTextareaKeyUp}
                  placeholder="הכנס הודעה או לחץ על משתנה להוספה..."
                  rows={8}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  מיקום הסמן: {cursorPosition !== null ? cursorPosition : 'לא זוהה'}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button onClick={handleTest} size="sm">
                  בדוק הודעה
                </Button>
                <Button onClick={handleClear} variant="outline" size="sm">
                  נקה
                </Button>
              </div>
            </div>

            {/* Variable Picker */}
            <div>
              <VariablePicker 
                onVariableSelect={handleVariableSelect}
                className="h-fit"
              />
            </div>
          </div>

          {/* Instructions */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <h4 className="font-medium text-blue-800 mb-2">הוראות שימוש:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• לחץ במקום כלשהו בתיבת הטקסט כדי להציב את הסמן</li>
                <li>• לחץ על משתנה כדי להוסיף אותו במיקום הסמן</li>
                <li>• אם לא נבחר מיקום, המשתנה יתווסף בסוף ההודעה</li>
                <li>• השתמש בהודעות המוכנות לבדיקה מהירה</li>
                <li>• כל המשתנים מאורגנים בקטגוריות: ליד, חברה, משתמש מוקצה</li>
              </ul>
            </CardContent>
          </Card>

        </CardContent>
      </Card>
    </div>
  );
};
