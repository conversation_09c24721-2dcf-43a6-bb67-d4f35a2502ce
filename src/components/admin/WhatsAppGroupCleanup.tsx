import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Trash2, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface CleanupResult {
  conversationsDeleted: number;
  messagesDeleted: number;
  groupChatsFound: string[];
}

export function WhatsAppGroupCleanup() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<CleanupResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const runCleanup = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setResult(null);

      const { data, error: functionError } = await supabase.functions.invoke('cleanup-whatsapp-groups');

      if (functionError) {
        throw new Error(functionError.message);
      }

      if (!data.success) {
        throw new Error(data.error || 'Cleanup failed');
      }

      setResult(data.result);
      toast.success('WhatsApp groups cleanup completed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      toast.error(`Cleanup failed: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="h-5 w-5" />
          WhatsApp Groups Cleanup
        </CardTitle>
        <CardDescription>
          Remove WhatsApp group conversations from the database. Only private conversations will be kept.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Warning:</strong> This action will permanently delete all WhatsApp group conversations 
            and their messages from the database. This cannot be undone.
          </AlertDescription>
        </Alert>

        <div className="space-y-2">
          <h4 className="font-medium">What this cleanup does:</h4>
          <ul className="text-sm text-muted-foreground space-y-1 ml-4">
            <li>• Identifies conversations with chat IDs ending in @g.us (WhatsApp groups)</li>
            <li>• Deletes these group conversations and all their messages</li>
            <li>• Keeps all private conversations (chat IDs ending in @c.us)</li>
            <li>• Provides a detailed report of what was deleted</li>
          </ul>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <div><strong>Cleanup completed successfully!</strong></div>
                <div>Conversations deleted: {result.conversationsDeleted}</div>
                <div>Messages deleted: {result.messagesDeleted}</div>
                {result.groupChatsFound.length > 0 && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm font-medium">
                      View deleted group chats ({result.groupChatsFound.length})
                    </summary>
                    <div className="mt-2 text-xs space-y-1">
                      {result.groupChatsFound.map((chatId, index) => (
                        <div key={index} className="font-mono bg-muted p-1 rounded">
                          {chatId}
                        </div>
                      ))}
                    </div>
                  </details>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        <Button 
          onClick={runCleanup} 
          disabled={isLoading}
          variant="destructive"
          className="w-full"
        >
          {isLoading ? 'Running Cleanup...' : 'Run WhatsApp Groups Cleanup'}
        </Button>
      </CardContent>
    </Card>
  );
}
