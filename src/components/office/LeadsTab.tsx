import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { LeadModal } from "./LeadModal";
import { LeadsList } from "@/components/leads/LeadsList";
import { ActiveCallBar } from "@/components/leads/ActiveCallBar";
import { useLeads } from "@/hooks/useLeads";
import { useCallManager } from "@/hooks/useCallManager";
import { Lead } from "@/components/leads/LeadCard";
import { getLeadStatusColorOffice } from "@/constants/leadStatuses";

export const LeadsTab = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const { leads, isLoading, addLead, updateLead } = useLeads();
  const {
    activeCall,
    isLoading: isCallLoading,
    initiateCall,
    hangupCall,
    muteCall,
    unmuteCall,
    acceptCall,
    rejectCall,
    isMuted,
    device,
    voiceCall
  } = useCallManager();



  const handleEdit = (lead: Lead) => {
    setEditingLead(lead);
    setIsModalOpen(true);
  };

  const handleAddLead = async (leadData: Omit<Lead, 'id' | 'created_at' | 'updated_at'>) => {
    return await addLead(leadData);
  };

  const handleUpdateLead = async (leadId: string, updates: Partial<Lead>) => {
    return await updateLead(leadId, updates);
  };

  const handleWhatsApp = (phoneNumber: string) => {
    // Find the lead by phone number to get the lead ID
    const lead = leads.find(l => l.phone === phoneNumber);
    if (lead) {
      navigate(`/office/whatsapp?lead=${lead.id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-foreground">לידים</h2>
            <p className="text-foreground text-sm font-medium">טוען...</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-96 bg-muted animate-pulse rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Active Call Bar */}
      {activeCall && (
        <ActiveCallBar
          activeCall={activeCall}
          onHangup={hangupCall}
          onMute={muteCall}
          onUnmute={unmuteCall}
          onAccept={acceptCall}
          onReject={rejectCall}
          isMuted={isMuted}
        />
      )}

      {/* Header */}
      <div className={`flex items-center justify-between ${activeCall ? 'mt-20' : ''}`}>
        <div>
          <h2 className="text-xl font-semibold text-foreground">לידים</h2>
          <p className="text-foreground text-sm font-medium">
            ניהול לידים ולקוחות פוטנציאליים ({leads.length} לידים)
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            className="btn-professional flex items-center gap-2"
            onClick={() => setIsModalOpen(true)}
          >
            <Plus className="w-4 h-4" />
            הוסף ליד חדש
          </Button>
        </div>
      </div>

      {/* Leads List */}
      <LeadsList
        leads={leads}
        onCall={initiateCall}
        onEdit={handleEdit}
        onWhatsApp={handleWhatsApp}
        isCallLoading={isCallLoading}
      />

      {/* Modal */}
      <LeadModal 
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingLead(null);
        }}
        editingLead={editingLead}
        onAddLead={handleAddLead}
        onUpdateLead={handleUpdateLead}
      />
    </div>
  );
};