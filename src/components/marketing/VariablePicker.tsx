import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, User, Building2, UserCheck } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Variable {
  key: string;
  label: string;
  description?: string;
  format: string;
}

interface VariableCategory {
  id: string;
  title: string;
  icon: React.ReactNode;
  variables: Variable[];
}

interface VariablePickerProps {
  onVariableSelect: (variable: string) => void;
  className?: string;
}

export const VariablePicker = ({ onVariableSelect, className }: VariablePickerProps) => {
  const [openCategories, setOpenCategories] = useState<string[]>(['lead']);

  const toggleCategory = (categoryId: string) => {
    setOpenCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const variableCategories: VariableCategory[] = [
    {
      id: 'lead',
      title: 'פרטי הליד',
      icon: <User className="h-4 w-4" />,
      variables: [
        {
          key: 'lead_name',
          label: 'שם הליד',
          description: 'השם המלא של הליד',
          format: '{lead_name}'
        },
        {
          key: 'lead_phone',
          label: 'טלפון הליד',
          description: 'מספר הטלפון של הליד',
          format: '{lead_phone}'
        },
        {
          key: 'lead_email',
          label: 'אימייל הליד',
          description: 'כתובת האימייל של הליד',
          format: '{lead_email}'
        },
        {
          key: 'lead_status',
          label: 'סטטוס הליד',
          description: 'הסטטוס הנוכחי של הליד',
          format: '{lead_status}'
        },
        {
          key: 'lead_value',
          label: 'ערך הליד',
          description: 'הערך הכספי של הליד',
          format: '{lead_value}'
        },
        {
          key: 'lead_source',
          label: 'מקור הליד',
          description: 'המקור ממנו הגיע הליד',
          format: '{lead_source}'
        },
        {
          key: 'lead_notes',
          label: 'הערות הליד',
          description: 'הערות נוספות על הליד',
          format: '{lead_notes}'
        }
      ]
    },
    {
      id: 'company',
      title: 'פרטי החברה',
      icon: <Building2 className="h-4 w-4" />,
      variables: [
        {
          key: 'company_name',
          label: 'שם החברה',
          description: 'שם החברה שלכם',
          format: '{company_name}'
        },
        {
          key: 'company_email',
          label: 'אימייל החברה',
          description: 'כתובת האימייל של החברה',
          format: '{company_email}'
        },
        {
          key: 'company_phone',
          label: 'טלפון החברה',
          description: 'מספר הטלפון של החברה',
          format: '{company_phone}'
        },
        {
          key: 'company_address',
          label: 'כתובת החברה',
          description: 'הכתובת הפיזית של החברה',
          format: '{company_address}'
        }
      ]
    },
    {
      id: 'assigned_user',
      title: 'המשתמש המוקצה',
      icon: <UserCheck className="h-4 w-4" />,
      variables: [
        {
          key: 'assigned_user_name',
          label: 'שם המשתמש המוקצה',
          description: 'השם של המשתמש שמוקצה לליד/תיק',
          format: '{assigned_user_name}'
        },
        {
          key: 'assigned_user_email',
          label: 'אימייל המשתמש המוקצה',
          description: 'כתובת האימייל של המשתמש המוקצה',
          format: '{assigned_user_email}'
        },
        {
          key: 'assigned_user_phone',
          label: 'טלפון המשתמש המוקצה',
          description: 'מספר הטלפון של המשתמש המוקצה',
          format: '{assigned_user_phone}'
        }
      ]
    }
  ];

  const handleVariableClick = (variable: Variable) => {
    onVariableSelect(variable.format);
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium">משתנים זמינים</CardTitle>
        <p className="text-xs text-muted-foreground">
          לחץ על משתנה כדי להוסיף אותו להודעה
        </p>
      </CardHeader>
      <CardContent className="space-y-2">
        {variableCategories.map((category) => (
          <Collapsible
            key={category.id}
            open={openCategories.includes(category.id)}
            onOpenChange={() => toggleCategory(category.id)}
          >
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-between p-2 h-auto font-normal"
              >
                <div className="flex items-center gap-2">
                  {category.icon}
                  <span className="text-sm">{category.title}</span>
                </div>
                {openCategories.includes(category.id) ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-1 mt-1">
              <div className="grid grid-cols-1 gap-1 pl-6">
                {category.variables.map((variable) => (
                  <Button
                    key={variable.key}
                    variant="outline"
                    size="sm"
                    className="justify-start h-auto p-2 text-xs hover:bg-primary/10"
                    onClick={() => handleVariableClick(variable)}
                    title={`${variable.description}\nפורמט: ${variable.format}`}
                  >
                    <div className="flex flex-col items-start gap-1 w-full">
                      <span className="font-medium">{variable.label}</span>
                      <Badge variant="secondary" className="text-xs font-mono">
                        {variable.format}
                      </Badge>
                    </div>
                  </Button>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        ))}
      </CardContent>
    </Card>
  );
};
