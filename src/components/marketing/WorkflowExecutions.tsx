import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { But<PERSON> } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { Activity, Clock, CheckCircle, AlertTriangle, RefreshCw, User, MessageSquare } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { he } from 'date-fns/locale';

interface WorkflowExecution {
  id: string;
  workflow_id: string;
  trigger_entity_type: string;
  trigger_entity_id: string;
  status: string;
  current_step_order: number;
  started_at: string;
  completed_at?: string;
  next_execution_at?: string;
  workflows: {
    name: string;
    trigger_type: string;
  };
  entity_name?: string; // Will be populated based on trigger_entity_type
  entity_phone?: string;
}

export const WorkflowExecutions = () => {
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentCompany } = useCompany();

  const fetchExecutions = async () => {
    if (!currentCompany?.id) {
      setExecutions([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('workflow_executions')
        .select(`
          *,
          workflows!inner (
            name,
            trigger_type
          )
        `)
        .eq('company_id', currentCompany.id)
        .order('started_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      // Populate entity data based on trigger_entity_type
      const executionsWithEntityData = await Promise.all(
        (data || []).map(async (execution) => {
          if (execution.trigger_entity_type === 'lead') {
            const { data: leadData } = await supabase
              .from('leads')
              .select('full_name, phone')
              .eq('id', execution.trigger_entity_id)
              .single();

            return {
              ...execution,
              entity_name: leadData?.full_name,
              entity_phone: leadData?.phone
            };
          } else if (execution.trigger_entity_type === 'case') {
            const { data: caseData } = await supabase
              .from('cases')
              .select('title, leads(full_name, phone)')
              .eq('id', execution.trigger_entity_id)
              .single();

            return {
              ...execution,
              entity_name: caseData?.title || caseData?.leads?.full_name,
              entity_phone: caseData?.leads?.phone
            };
          }

          return execution;
        })
      );

      setExecutions(executionsWithEntityData);
    } catch (error) {
      console.error('Error fetching executions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchExecutions();
  }, [currentCompany?.id]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'running':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'הושלם';
      case 'failed':
        return 'נכשל';
      case 'running':
        return 'פועל';
      case 'paused':
        return 'מושהה';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            יומן ביצועים
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchExecutions}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            רענן
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">טוען ביצועים...</p>
          </div>
        ) : executions.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4">
              <Activity className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">אין ביצועים עדיין</h3>
            <p className="text-muted-foreground">
              ביצועי זרימות העבודה יופיעו כאן לאחר הפעלתן
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[500px]">
            <div className="space-y-4">
              {executions.map((execution) => (
                <div
                  key={execution.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    {getStatusIcon(execution.status)}
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{execution.workflows.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {execution.workflows.trigger_type === 'manual' ? 'ידני' :
                           execution.workflows.trigger_type === 'lead_status_change' ? 'ליד' : 'תיק'}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <User className="h-3 w-3" />
                        <span>{execution.entity_name || 'לא ידוע'}</span>
                        {execution.entity_phone && (
                          <>
                            <span>•</span>
                            <span>{execution.entity_phone}</span>
                          </>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {formatDistanceToNow(new Date(execution.started_at), {
                          addSuffix: true,
                          locale: he
                        })}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <Badge className={`text-xs ${getStatusColor(execution.status)}`}>
                        {getStatusText(execution.status)}
                      </Badge>
                      <div className="text-xs text-muted-foreground mt-1">
                        שלב {execution.current_step_order}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
};
