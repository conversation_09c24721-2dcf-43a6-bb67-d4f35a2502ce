import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'sonner';
import { Company, CompanyOnboardingData } from '@/types/company';

interface SuperAdminStats {
  totalCompanies: number;
  activeCompanies: number;
  totalUsers: number;
  activeSubscriptions: number;
  newCompaniesThisMonth: number;
}

// OnboardingData is now imported from types/company.ts as CompanyOnboardingData

export const useSuperAdmin = () => {
  const { user, userRole } = useAuth();
  const { allCompanies, handleCompanyCreated, handleCompanyDeleted, handleCompanyUpdated, refreshCompanies } = useCompany();
  const [stats, setStats] = useState<SuperAdminStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if user is super admin
  const isSuperAdmin = userRole?.role === 'super_admin';

  const fetchCompanies = async () => {
    if (!isSuperAdmin) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Fetch companies with user count
      const { data: companiesData, error: companiesError } = await supabase
        .from('companies')
        .select(`
          *,
          user_roles(count)
        `)
        .order('created_at', { ascending: false });

      if (companiesError) throw companiesError;

      // Calculate stats using companies from CompanyContext
      const companies = allCompanies;
      const totalCompanies = companies.length;
      const activeCompanies = companies.filter(c => c.status === 'active').length;
      
      // Get total users across all companies
      const { count: totalUsers } = await supabase
        .from('user_roles')
        .select('*', { count: 'exact', head: true });

      // Get companies created this month
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const newCompaniesThisMonth = companies.filter(
        c => new Date(c.created_at) >= startOfMonth
      ).length;

      setStats({
        totalCompanies,
        activeCompanies,
        totalUsers: totalUsers || 0,
        activeSubscriptions: activeCompanies, // Assuming active companies have active subscriptions
        newCompaniesThisMonth
      });

    } catch (err) {
      console.error('Error fetching companies:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const createCompanyWithAdmin = async (data: CompanyOnboardingData) => {
    if (!isSuperAdmin) {
      throw new Error('Unauthorized: Only super admins can create companies');
    }

    try {
      // 1. Create the company
      // Generate a unique domain using timestamp to avoid duplicates
      const timestamp = Date.now();
      const baseSlug = data.companyName.toLowerCase().replace(/[^a-z0-9]/g, '');
      const uniqueDomain = `${baseSlug}-${timestamp}.local`;

      const { data: company, error: companyError } = await supabase
        .from('companies')
        .insert({
          name: data.companyName,
          domain: uniqueDomain, // Use unique domain to avoid conflicts
          email: data.companyEmail,
          phone: data.companyPhone,
          address: data.companyAddress,
          subscription_plan: data.subscriptionPlan,
          max_users: data.maxUsers,
          status: 'active',
          settings: {
            timezone: data.timezone,
            language: data.language,
            notes: data.notes
          },
          api_tokens: {}, // API tokens will be configured later in company settings
          onboarded_at: new Date().toISOString(),
          onboarded_by: user?.id
        })
        .select()
        .single();

      if (companyError) throw companyError;

      // 2. Create admin user using Supabase Edge Function
      console.log('Creating admin user for company:', company.id);

      const { data: adminUserResult, error: adminUserError } = await supabase.functions.invoke('create-company-admin', {
        body: {
          companyId: company.id,
          adminEmail: data.adminEmail,
          adminFirstName: data.adminFirstName,
          adminLastName: data.adminLastName,
          adminPhone: data.adminPhone
        }
      });

      if (adminUserError) {
        console.error('Error creating admin user:', adminUserError);
        // Don't fail the entire process, but log the error
        toast.error(`Company created but admin user creation failed: ${adminUserError.message}`);
        console.log('Admin details for manual setup:', {
          firstName: data.adminFirstName,
          lastName: data.adminLastName,
          email: data.adminEmail,
          phone: data.adminPhone,
          companyId: company.id
        });
      } else if (adminUserResult?.success) {
        console.log('Admin user created successfully:', adminUserResult);
        toast.success(`Admin user created! Temporary password: ${adminUserResult.message?.split('Temporary password: ')[1] || 'Check logs'}`);
      } else {
        console.error('Admin user creation failed:', adminUserResult);
        toast.error(`Admin user creation failed: ${adminUserResult?.error || 'Unknown error'}`);
      }

      // 3. Create default case types for the company
      const defaultCaseTypes = [
        { name: 'ייעוץ משפטי', hourly_rate: 500, company_id: company.id },
        { name: 'ליטיגציה', hourly_rate: 800, company_id: company.id },
        { name: 'נדל"ן', hourly_rate: 600, company_id: company.id },
        { name: 'דיני עבודה', hourly_rate: 550, company_id: company.id }
      ];

      const { error: caseTypesError } = await supabase
        .from('case_types')
        .insert(defaultCaseTypes);

      if (caseTypesError) throw caseTypesError;

      // 4. Create default workflows for the company
      console.log('Creating default workflows for company:', company.id);

      const { data: workflowsResult, error: workflowsError } = await supabase.functions.invoke('create-default-workflows', {
        body: {
          companyId: company.id,
          adminUserId: adminUserResult.userId
        }
      });

      if (workflowsError) {
        console.error('Error creating default workflows:', workflowsError);
        toast.error('Failed to create default workflows');
      } else if (workflowsResult?.success) {
        console.log('Default workflows created successfully:', workflowsResult);
        toast.success(`Created ${workflowsResult.workflows?.length || 0} default workflows`);
      }

      // Use CompanyContext to handle the update
      await handleCompanyCreated(company);

      toast.success(`Company "${data.companyName}" created successfully with admin user!`);

      return company;

    } catch (error) {
      console.error('Error creating company:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('email')) {
          toast.error('Invalid email format. Please check the company email address.');
        } else if (error.message.includes('phone')) {
          toast.error('Invalid phone number format. Please check the phone number.');
        } else if (error.message.includes('duplicate')) {
          toast.error('A company with this information already exists.');
        } else {
          toast.error(`Failed to create company: ${error.message}`);
        }
      } else {
        toast.error('An unexpected error occurred while creating the company.');
      }

      throw error;
    }
  };

  const updateCompanyStatus = async (companyId: string, status: 'active' | 'suspended' | 'inactive') => {
    if (!isSuperAdmin) {
      throw new Error('Unauthorized: Only super admins can update company status');
    }

    try {
      const { error } = await supabase
        .from('companies')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', companyId);

      if (error) throw error;

      await refreshCompanies();
      toast.success('Company status updated successfully');

    } catch (error) {
      console.error('Error updating company status:', error);
      throw error;
    }
  };

  const deleteCompany = async (companyId: string) => {
    if (!isSuperAdmin) {
      throw new Error('Unauthorized: Only super admins can delete companies');
    }

    try {
      // Use the new company deletion Edge Function for comprehensive cleanup
      const { data, error } = await supabase.functions.invoke('company-deletion', {
        body: { companyId }
      });

      if (error) throw error;

      if (data?.error) {
        throw new Error(data.error);
      }

      await handleCompanyDeleted(companyId);
      toast.success(data?.message || 'Company deleted successfully');

    } catch (error) {
      console.error('Error deleting company:', error);
      throw error;
    }
  };

  useEffect(() => {
    if (user && isSuperAdmin) {
      fetchCompanies();
    }
  }, [user, isSuperAdmin]);

  return {
    companies: allCompanies,
    stats,
    isLoading,
    error,
    isSuperAdmin,
    createCompanyWithAdmin,
    updateCompanyStatus,
    deleteCompany,
    refetch: refreshCompanies
  };
};
