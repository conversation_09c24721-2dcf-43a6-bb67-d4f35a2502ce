/**
 * Lead Status Constants
 * Centralized definition of all lead statuses and their styling
 */

export const LEAD_STATUSES = [
  "ליד חדש",
  "צריך פולוא<PERSON>",
  "לקוח סגור",
  "לא ענה",
  "לא מעוניין",
  "לא מתאים"
] as const;

export type LeadStatus = typeof LEAD_STATUSES[number];

/**
 * Get the appropriate CSS classes for a lead status badge
 */
export const getLeadStatusColor = (status: string): string => {
  switch (status) {
    case "ליד חדש":
      return "bg-primary text-primary-foreground";
    case "צריך פולואפ":
      return "bg-warning text-warning-foreground";
    case "לקוח סגור":
      return "bg-success text-success-foreground";
    case "לא ענה":
    case "לא מעוניין":
    case "לא מתאים":
      return "bg-muted text-muted-foreground";
    default:
      return "bg-muted text-muted-foreground";
  }
};

/**
 * Get the appropriate CSS classes for a lead status in office/leads tab (with transparency)
 */
export const getLeadStatusColorOffice = (status: string): string => {
  switch (status) {
    case "ליד חדש":
      return "bg-primary/10 text-primary border-primary/20";
    case "צריך פולואפ":
      return "bg-warning/10 text-warning border-warning/20";
    case "לקוח סגור":
      return "bg-success/10 text-success border-success/20";
    case "לא ענה":
    case "לא מעוניין":
    case "לא מתאים":
      return "bg-muted text-muted-foreground border-border";
    default:
      return "bg-muted text-muted-foreground border-border";
  }
};
