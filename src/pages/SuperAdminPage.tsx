import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Building2, Users, Plus, Settings, Eye, Trash2 } from 'lucide-react';
import { CompanyOnboardingForm } from '@/components/admin/CompanyOnboardingForm';
import { PerformanceMonitor } from '@/components/admin/PerformanceMonitor';
import { CompanyManagement } from '@/components/admin/CompanyManagement';
import { SuperAdminUserManagement } from '@/components/admin/SuperAdminUserManagement';
import { WhatsAppGroupCleanup } from '@/components/admin/WhatsAppGroupCleanup';
import { useAuth } from '@/hooks/useAuth';
import { useSuperAdmin } from '@/hooks/useSuperAdmin';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

const SuperAdminPage = () => {
  const [activeTab, setActiveTab] = useState('companies');
  const [showOnboardingForm, setShowOnboardingForm] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<any>(null);
  const { user, userRole } = useAuth();
  const { companies, stats, isLoading, refetch } = useSuperAdmin();

  const handleManageCompany = (company: any) => {
    setSelectedCompany(company);
  };

  const handleCloseCompanyManagement = () => {
    setSelectedCompany(null);
  };

  const handleCompanyUpdate = () => {
    refetch();
  };

  // Debug: Log current user and role
  console.log('SuperAdminPage - Current user:', user?.id);
  console.log('SuperAdminPage - Current userRole:', userRole);

  // Helper function to make current user super admin (for development)
  const makeCurrentUserSuperAdmin = async () => {
    if (!user?.id) return;

    try {
      // First, check if user already has any role
      const { data: existingRole } = await supabase
        .from('user_roles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (existingRole) {
        // Update existing role to super_admin
        const { data, error } = await supabase
          .from('user_roles')
          .update({ role: 'super_admin' })
          .eq('user_id', user.id);

        if (error) {
          console.error('Error updating user to super admin:', error);
        } else {
          console.log('Successfully updated user to super admin:', data);
          window.location.reload();
        }
      } else {
        // Insert new super admin role
        const { data, error } = await supabase
          .from('user_roles')
          .insert({
            user_id: user.id,
            role: 'super_admin',
            company_id: null
          });

        if (error) {
          console.error('Error creating super admin role:', error);
        } else {
          console.log('Successfully created super admin role:', data);
          window.location.reload();
        }
      }
    } catch (err) {
      console.error('Exception making user super admin:', err);
    }
  };

  // Check if user is super admin
  if (!userRole || userRole.role !== 'super_admin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-center text-red-600">Access Denied</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">
              You don't have permission to access the Super Admin portal.
            </p>
            <div className="mt-4 text-xs text-gray-500">
              <p>User ID: {user?.id || 'Not logged in'}</p>
              <p>Role: {userRole?.role || 'No role found'}</p>
              <p>Company ID: {userRole?.company_id || 'No company'}</p>
            </div>
            {user?.id && (
              <div className="mt-4">
                <Button
                  onClick={makeCurrentUserSuperAdmin}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  Make Current User Super Admin (Dev Only)
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading Super Admin Portal...</p>
        </div>
      </div>
    );
  }

  // If a company is selected, show company management
  if (selectedCompany) {
    return (
      <div className="container mx-auto p-6">
        <CompanyManagement
          company={selectedCompany}
          onClose={handleCloseCompanyManagement}
          onUpdate={handleCompanyUpdate}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Super Admin Portal</h1>
          <p className="text-muted-foreground">Manage companies and system-wide settings</p>
        </div>
        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
          Super Admin
        </Badge>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Companies</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalCompanies || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.activeCompanies || 0} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">
              Across all companies
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.activeSubscriptions || 0}</div>
            <p className="text-xs text-muted-foreground">
              Paying customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Plus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.newCompaniesThisMonth || 0}</div>
            <p className="text-xs text-muted-foreground">
              New companies
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="companies">Companies</TabsTrigger>
          <TabsTrigger value="users">All Users</TabsTrigger>
          <TabsTrigger value="onboarding">Onboard New Company</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="settings">System Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="companies" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Company Management</h2>
            <Button 
              onClick={() => {
                setActiveTab('onboarding');
                setShowOnboardingForm(true);
              }}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Onboard New Company
            </Button>
          </div>
          
          {companies.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">No Companies Yet</h3>
              <p className="text-sm text-muted-foreground">
                Create your first company using the onboarding form
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {companies.map((company) => (
                <Card key={company.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Building2 className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{company.name}</h3>
                          <p className="text-sm text-muted-foreground">{company.email}</p>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-xs text-muted-foreground">
                              {company.user_count || 0} users
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {company.subscription_plan}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Created {new Date(company.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={company.status === 'active' ? 'default' : 'secondary'}
                        >
                          {company.status}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleManageCompany(company)}
                        >
                          ניהול חברה
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <SuperAdminUserManagement />
        </TabsContent>

        <TabsContent value="onboarding" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-2">Company Onboarding</h2>
            <p className="text-muted-foreground">
              Create a new company and set up their initial admin user with all required configurations.
            </p>
          </div>
          
          <CompanyOnboardingForm 
            onSuccess={() => {
              setActiveTab('companies');
              setShowOnboardingForm(false);
            }}
            onCancel={() => {
              setActiveTab('companies');
              setShowOnboardingForm(false);
            }}
          />
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <PerformanceMonitor />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-2">System Settings</h2>
            <p className="text-muted-foreground">
              Configure system-wide settings and preferences.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>System Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="text-sm font-medium">Default Subscription Plan</label>
                    <select className="w-full mt-1 p-2 border rounded-md">
                      <option value="basic">Basic</option>
                      <option value="pro">Pro</option>
                      <option value="enterprise">Enterprise</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Default Max Users</label>
                    <input
                      type="number"
                      defaultValue={10}
                      className="w-full mt-1 p-2 border rounded-md"
                    />
                  </div>
                </div>

                <Button>Save Settings</Button>
              </CardContent>
            </Card>

            <WhatsAppGroupCleanup />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SuperAdminPage;
