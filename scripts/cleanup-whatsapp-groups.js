#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to clean up WhatsApp group conversations from the database
 * This script calls the cleanup-whatsapp-groups Supabase Edge Function
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function runCleanup() {
  try {
    console.log('🧹 Starting WhatsApp groups cleanup...');
    
    // Note: This requires authentication as a super admin
    // You'll need to sign in first or provide auth token
    console.log('⚠️  This operation requires super admin authentication.');
    console.log('Please run this from the application as a super admin user.');
    console.log('');
    console.log('You can call the cleanup function from your app using:');
    console.log('');
    console.log('const { data, error } = await supabase.functions.invoke("cleanup-whatsapp-groups");');
    console.log('');
    console.log('Or use the Supabase dashboard to invoke the function directly.');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    process.exit(1);
  }
}

runCleanup();
