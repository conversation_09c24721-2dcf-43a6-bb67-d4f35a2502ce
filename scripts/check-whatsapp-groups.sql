-- Check for WhatsApp group conversations in the database
-- Groups have chat IDs ending with @g.us

-- Count total conversations
SELECT 
  'Total Conversations' as type,
  COUNT(*) as count
FROM whatsapp_conversations
WHERE green_api_chat_id IS NOT NULL

UNION ALL

-- Count private conversations (@c.us)
SELECT 
  'Private Conversations' as type,
  COUNT(*) as count
FROM whatsapp_conversations
WHERE green_api_chat_id LIKE '%@c.us'

UNION ALL

-- Count group conversations (@g.us)
SELECT 
  'Group Conversations' as type,
  COUNT(*) as count
FROM whatsapp_conversations
WHERE green_api_chat_id LIKE '%@g.us'

UNION ALL

-- Count conversations with unknown format
SELECT 
  'Unknown Format' as type,
  COUNT(*) as count
FROM whatsapp_conversations
WHERE green_api_chat_id IS NOT NULL 
  AND green_api_chat_id NOT LIKE '%@c.us' 
  AND green_api_chat_id NOT LIKE '%@g.us';

-- Show details of group conversations (if any)
SELECT 
  id,
  company_id,
  green_api_chat_id,
  contact_name,
  phone_number,
  last_message,
  created_at
FROM whatsapp_conversations
WHERE green_api_chat_id LIKE '%@g.us'
ORDER BY created_at DESC;

-- Count messages in group conversations
SELECT 
  'Messages in Group Conversations' as description,
  COUNT(m.*) as message_count
FROM whatsapp_messages m
JOIN whatsapp_conversations c ON m.conversation_id = c.id
WHERE c.green_api_chat_id LIKE '%@g.us';
